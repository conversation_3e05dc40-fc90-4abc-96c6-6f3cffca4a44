# 常用模式和最佳实践

- 实现了腾讯云IM回调数据转换为TUIKit消息格式的功能。在 src/components/TUIKit/components/TUIChat/message-list/index.vue 中添加了 convertIMCallbackToTUIKitMessage 转换方法和 handleIMCallback 处理方法，支持将腾讯云IM回调数据转换为TUIKit消息格式并添加到消息列表前面。方法已通过 defineExpose 暴露给外部调用。
- 在UploadFile组件中实现了文件分片上传功能：1)扩展OssClient类添加multipartUpload方法，支持分片大小、并发数和进度回调配置；2)大文件(>10MB)自动使用分片上传，提供真实进度反馈；3)提升文件大小限制至视频500MB、图片100MB；4)分片上传具备断点续传、并发传输、网络容错等优势
- 实现了删除历史记录消息功能：1)在chatHistory.ts中添加deleteHistoryMessages API接口，路径为/v1/txy/im/anon/delete/im/message，参数格式为{integers: [originalIds]}；2)修改TUIChat/index.vue中的deleteMessage函数，通过message.originalId字段区分历史记录消息和普通消息；3)历史记录消息调用新API，普通消息使用原有删除逻辑；4)支持混合消息类型的批量删除，使用Promise.all并行处理
- 在聊天历史模态框中实现了删除当前会话时自动新建对话的功能：当删除的会话sessionId与chatStore.sessionId匹配时，自动调用createGroupSession API创建新会话，清空历史消息并设置新的sessionId，确保用户删除当前会话后能继续正常使用聊天功能
